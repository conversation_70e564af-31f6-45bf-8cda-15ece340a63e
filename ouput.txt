data: {"type": "reference", "content": "[{\"id\": \"f8a6abeb-1eba-4e1e-b845-e3b045ecf250\", \"content\": \"<strong>会议主题：2022 年 9 月 21 号 </strong><strong>PCB</strong><strong>评审群组会议纪要</strong>\\n3 M9B M9 <strong>---9 月 29 号 堆叠锁定</strong><strong> </strong><strong> </strong>\\n1. PMU 与电感与射频 PA 本体不要上下重叠\\n2. 射频 2G PA 距离原则 15MM\\n3. 射频 U0600PA 与对应滤波器就近放置，不要太远\\n4. 定位孔建议右下角与左上角\\n5. CABLE 座子 M9 与 M9B 需要保持一致，尽量考虑公共端最短\\n6. Camera 孔 主摄与微距开孔联通，M9 M9B 布局重新调整\\n7. 充电芯片是否可以胶水可以粘胶？\\n8. 主芯片屏蔽罩是否需要两件形式的？需要确认，目前单体屏蔽罩涂导热凝胶有覆盖不全风险\", \"user_id\": \"pcb_kb_user\", \"metadata_json\": {\"Header 1\": \"<strong>会议主题：2022 年 9 月 21 号 </strong><strong>PCB</strong><strong>评审群组会议纪要</strong>\", \"Header 2\": \"3 M9B M9 <strong>---9 月 29 号 堆叠锁定</strong><strong> </strong><strong> </strong>\", \"id\": \"20902\", \"doc_type\": \"sheet\", \"publish_time\": \"2025-04-28 19:03:34\", \"project_area\": \"011\", \"doc_url\": \"https://xiaomi.f.mioffice.cn/sheets/shtk4sDk1eD6kdBnPcqzfFq7q1g\", \"doc_name\": \"FPC板厂工艺时间表\", \"tm\": \"2025-05-06 19:23:42\", \"importance\": 1.0}, \"score\": 0.032018442622950824}]", "role": "", "finish_reason": "", "msg_id": "YOUR_UUID", "conversation_id": "YOUR_UUID"}

data: {"type": "content", "content": "", "role": "assistant", "finish_reason": "", "msg_id": "YOUR_UUID", "conversation_id": "YOUR_UUID"}

data: {"type": "reasoning", "content": "\n", "role": "", "finish_reason": "", "msg_id": "YOUR_UUID", "conversation_id": "YOUR_UUID"}

data: {"type": "reasoning", "content": "好的", "role": "", "finish_reason": "", "msg_id": "YOUR_UUID", "conversation_id": "YOUR_UUID"}

data: {"type": "reasoning", "content": "，", "role": "", "finish_reason": "", "msg_id": "YOUR_UUID", "conversation_id": "YOUR_UUID"}

data: {"type": "reasoning", "content": "我现在", "role": "", "finish_reason": "", "msg_id": "YOUR_UUID", "conversation_id": "YOUR_UUID"}

data: {"type": "reasoning", "content": "需要", "role": "", "finish_reason": "", "msg_id": "YOUR_UUID", "conversation_id": "YOUR_UUID"}

data: {"type": "reasoning", "content": "处理", "role": "", "finish_reason": "", "msg_id": "YOUR_UUID", "conversation_id": "YOUR_UUID"}

data: {"type": "reasoning", "content": "用户", "role": "", "finish_reason": "", "msg_id": "YOUR_UUID", "conversation_id": "YOUR_UUID"}

data: {"type": "reasoning", "content": "的问题", "role": "", "finish_reason": "", "msg_id": "YOUR_UUID", "conversation_id": "YOUR_UUID"}

data: {"type": "reasoning", "content": "：“", "role": "", "finish_reason": "", "msg_id": "YOUR_UUID", "conversation_id": "YOUR_UUID"}

data: {"type": "reasoning", "content": "M", "role": "", "finish_reason": "", "msg_id": "YOUR_UUID", "conversation_id": "YOUR_UUID"}

data: {"type": "reasoning", "content": "9", "role": "", "finish_reason": "", "msg_id": "YOUR_UUID", "conversation_id": "YOUR_UUID"}

data: {"type": "reasoning", "content": "项目", "role": "", "finish_reason": "", "msg_id": "YOUR_UUID", "conversation_id": "YOUR_UUID"}

data: {"type": "reasoning", "content": "什么时候", "role": "", "finish_reason": "", "msg_id": "YOUR_UUID", "conversation_id": "YOUR_UUID"}

data: {"type": "reasoning", "content": "需要", "role": "", "finish_reason": "", "msg_id": "YOUR_UUID", "conversation_id": "YOUR_UUID"}

data: {"type": "reasoning", "content": "挂", "role": "", "finish_reason": "", "msg_id": "YOUR_UUID", "conversation_id": "YOUR_UUID"}

data: {"type": "reasoning", "content": "板", "role": "", "finish_reason": "", "msg_id": "YOUR_UUID", "conversation_id": "YOUR_UUID"}

data: {"type": "reasoning", "content": "？”", "role": "", "finish_reason": "", "msg_id": "YOUR_UUID", "conversation_id": "YOUR_UUID"}

data: {"type": "reasoning", "content": "首先", "role": "", "finish_reason": "", "msg_id": "YOUR_UUID", "conversation_id": "YOUR_UUID"}

data: {"type": "reasoning", "content": "，", "role": "", "finish_reason": "", "msg_id": "YOUR_UUID", "conversation_id": "YOUR_UUID"}

data: {"type": "reasoning", "content": "根据", "role": "", "finish_reason": "", "msg_id": "YOUR_UUID", "conversation_id": "YOUR_UUID"}

data: {"type": "reasoning", "content": "规则", "role": "", "finish_reason": "", "msg_id": "YOUR_UUID", "conversation_id": "YOUR_UUID"}

data: {"type": "reasoning", "content": "，", "role": "", "finish_reason": "", "msg_id": "YOUR_UUID", "conversation_id": "YOUR_UUID"}

data: {"type": "reasoning", "content": "用户", "role": "", "finish_reason": "", "msg_id": "YOUR_UUID", "conversation_id": "YOUR_UUID"}

data: {"type": "reasoning", "content": "提到", "role": "", "finish_reason": "", "msg_id": "YOUR_UUID", "conversation_id": "YOUR_UUID"}

data: {"type": "reasoning", "content": "的", "role": "", "finish_reason": "", "msg_id": "YOUR_UUID", "conversation_id": "YOUR_UUID"}

data: {"type": "reasoning", "content": "“", "role": "", "finish_reason": "", "msg_id": "YOUR_UUID", "conversation_id": "YOUR_UUID"}

data: {"type": "reasoning", "content": "挂", "role": "", "finish_reason": "", "msg_id": "YOUR_UUID", "conversation_id": "YOUR_UUID"}

data: {"type": "reasoning", "content": "板", "role": "", "finish_reason": "", "msg_id": "YOUR_UUID", "conversation_id": "YOUR_UUID"}

data: {"type": "reasoning", "content": "”", "role": "", "finish_reason": "", "msg_id": "YOUR_UUID", "conversation_id": "YOUR_UUID"}

data: {"type": "reasoning", "content": "应该", "role": "", "finish_reason": "", "msg_id": "YOUR_UUID", "conversation_id": "YOUR_UUID"}

data: {"type": "reasoning", "content": "对应", "role": "", "finish_reason": "", "msg_id": "YOUR_UUID", "conversation_id": "YOUR_UUID"}

data: {"type": "reasoning", "content": "知识", "role": "", "finish_reason": "", "msg_id": "YOUR_UUID", "conversation_id": "YOUR_UUID"}

data: {"type": "reasoning", "content": "库", "role": "", "finish_reason": "", "msg_id": "YOUR_UUID", "conversation_id": "YOUR_UUID"}

data: {"type": "reasoning", "content": "中的", "role": "", "finish_reason": "", "msg_id": "YOUR_UUID", "conversation_id": "YOUR_UUID"}

data: {"type": "reasoning", "content": "“", "role": "", "finish_reason": "", "msg_id": "YOUR_UUID", "conversation_id": "YOUR_UUID"}

data: {"type": "reasoning", "content": "投", "role": "", "finish_reason": "", "msg_id": "YOUR_UUID", "conversation_id": "YOUR_UUID"}

data: {"type": "reasoning", "content": "板", "role": "", "finish_reason": "", "msg_id": "YOUR_UUID", "conversation_id": "YOUR_UUID"}

data: {"type": "reasoning", "content": "”。", "role": "", "finish_reason": "", "msg_id": "YOUR_UUID", "conversation_id": "YOUR_UUID"}

data: {"type": "reasoning", "content": "接下来", "role": "", "finish_reason": "", "msg_id": "YOUR_UUID", "conversation_id": "YOUR_UUID"}

data: {"type": "reasoning", "content": "，", "role": "", "finish_reason": "", "msg_id": "YOUR_UUID", "conversation_id": "YOUR_UUID"}

data: {"type": "reasoning", "content": "我要", "role": "", "finish_reason": "", "msg_id": "YOUR_UUID", "conversation_id": "YOUR_UUID"}

data: {"type": "reasoning", "content": "在", "role": "", "finish_reason": "", "msg_id": "YOUR_UUID", "conversation_id": "YOUR_UUID"}

data: {"type": "reasoning", "content": "项目", "role": "", "finish_reason": "", "msg_id": "YOUR_UUID", "conversation_id": "YOUR_UUID"}

data: {"type": "reasoning", "content": "编码", "role": "", "finish_reason": "", "msg_id": "YOUR_UUID", "conversation_id": "YOUR_UUID"}

data: {"type": "reasoning", "content": "为", "role": "", "finish_reason": "", "msg_id": "YOUR_UUID", "conversation_id": "YOUR_UUID"}

data: {"type": "reasoning", "content": "M", "role": "", "finish_reason": "", "msg_id": "YOUR_UUID", "conversation_id": "YOUR_UUID"}

data: {"type": "reasoning", "content": "9", "role": "", "finish_reason": "", "msg_id": "YOUR_UUID", "conversation_id": "YOUR_UUID"}

data: {"type": "reasoning", "content": "的", "role": "", "finish_reason": "", "msg_id": "YOUR_UUID", "conversation_id": "YOUR_UUID"}

data: {"type": "reasoning", "content": "条", "role": "", "finish_reason": "", "msg_id": "YOUR_UUID", "conversation_id": "YOUR_UUID"}

data: {"type": "reasoning", "content": "目", "role": "", "finish_reason": "", "msg_id": "YOUR_UUID", "conversation_id": "YOUR_UUID"}

data: {"type": "reasoning", "content": "里", "role": "", "finish_reason": "", "msg_id": "YOUR_UUID", "conversation_id": "YOUR_UUID"}

data: {"type": "reasoning", "content": "查找", "role": "", "finish_reason": "", "msg_id": "YOUR_UUID", "conversation_id": "YOUR_UUID"}

data: {"type": "reasoning", "content": "里程碑", "role": "", "finish_reason": "", "msg_id": "YOUR_UUID", "conversation_id": "YOUR_UUID"}

data: {"type": "reasoning", "content": "名称", "role": "", "finish_reason": "", "msg_id": "YOUR_UUID", "conversation_id": "YOUR_UUID"}

data: {"type": "reasoning", "content": "为", "role": "", "finish_reason": "", "msg_id": "YOUR_UUID", "conversation_id": "YOUR_UUID"}

data: {"type": "reasoning", "content": "“", "role": "", "finish_reason": "", "msg_id": "YOUR_UUID", "conversation_id": "YOUR_UUID"}

data: {"type": "reasoning", "content": "投", "role": "", "finish_reason": "", "msg_id": "YOUR_UUID", "conversation_id": "YOUR_UUID"}

data: {"type": "reasoning", "content": "板", "role": "", "finish_reason": "", "msg_id": "YOUR_UUID", "conversation_id": "YOUR_UUID"}

data: {"type": "reasoning", "content": "”的", "role": "", "finish_reason": "", "msg_id": "YOUR_UUID", "conversation_id": "YOUR_UUID"}

data: {"type": "reasoning", "content": "时间节点", "role": "", "finish_reason": "", "msg_id": "YOUR_UUID", "conversation_id": "YOUR_UUID"}

data: {"type": "reasoning", "content": "。", "role": "", "finish_reason": "", "msg_id": "YOUR_UUID", "conversation_id": "YOUR_UUID"}

data: {"type": "reasoning", "content": "查看", "role": "", "finish_reason": "", "msg_id": "YOUR_UUID", "conversation_id": "YOUR_UUID"}

data: {"type": "reasoning", "content": "M", "role": "", "finish_reason": "", "msg_id": "YOUR_UUID", "conversation_id": "YOUR_UUID"}

data: {"type": "reasoning", "content": "9", "role": "", "finish_reason": "", "msg_id": "YOUR_UUID", "conversation_id": "YOUR_UUID"}

data: {"type": "reasoning", "content": "的", "role": "", "finish_reason": "", "msg_id": "YOUR_UUID", "conversation_id": "YOUR_UUID"}

data: {"type": "reasoning", "content": "里程碑", "role": "", "finish_reason": "", "msg_id": "YOUR_UUID", "conversation_id": "YOUR_UUID"}

data: {"type": "reasoning", "content": "列表", "role": "", "finish_reason": "", "msg_id": "YOUR_UUID", "conversation_id": "YOUR_UUID"}

data: {"type": "reasoning", "content": "，", "role": "", "finish_reason": "", "msg_id": "YOUR_UUID", "conversation_id": "YOUR_UUID"}

data: {"type": "reasoning", "content": "发现", "role": "", "finish_reason": "", "msg_id": "YOUR_UUID", "conversation_id": "YOUR_UUID"}

data: {"type": "reasoning", "content": "投", "role": "", "finish_reason": "", "msg_id": "YOUR_UUID", "conversation_id": "YOUR_UUID"}

data: {"type": "reasoning", "content": "板", "role": "", "finish_reason": "", "msg_id": "YOUR_UUID", "conversation_id": "YOUR_UUID"}

data: {"type": "reasoning", "content": "的", "role": "", "finish_reason": "", "msg_id": "YOUR_UUID", "conversation_id": "YOUR_UUID"}

data: {"type": "reasoning", "content": "计划", "role": "", "finish_reason": "", "msg_id": "YOUR_UUID", "conversation_id": "YOUR_UUID"}

data: {"type": "reasoning", "content": "开始", "role": "", "finish_reason": "", "msg_id": "YOUR_UUID", "conversation_id": "YOUR_UUID"}

data: {"type": "reasoning", "content": "时间", "role": "", "finish_reason": "", "msg_id": "YOUR_UUID", "conversation_id": "YOUR_UUID"}

data: {"type": "reasoning", "content": "是", "role": "", "finish_reason": "", "msg_id": "YOUR_UUID", "conversation_id": "YOUR_UUID"}

data: {"type": "reasoning", "content": "2", "role": "", "finish_reason": "", "msg_id": "YOUR_UUID", "conversation_id": "YOUR_UUID"}

data: {"type": "reasoning", "content": "0", "role": "", "finish_reason": "", "msg_id": "YOUR_UUID", "conversation_id": "YOUR_UUID"}

data: {"type": "reasoning", "content": "2", "role": "", "finish_reason": "", "msg_id": "YOUR_UUID", "conversation_id": "YOUR_UUID"}

data: {"type": "reasoning", "content": "2", "role": "", "finish_reason": "", "msg_id": "YOUR_UUID", "conversation_id": "YOUR_UUID"}

data: {"type": "reasoning", "content": "-", "role": "", "finish_reason": "", "msg_id": "YOUR_UUID", "conversation_id": "YOUR_UUID"}

data: {"type": "reasoning", "content": "1", "role": "", "finish_reason": "", "msg_id": "YOUR_UUID", "conversation_id": "YOUR_UUID"}

data: {"type": "reasoning", "content": "1", "role": "", "finish_reason": "", "msg_id": "YOUR_UUID", "conversation_id": "YOUR_UUID"}

data: {"type": "reasoning", "content": "-", "role": "", "finish_reason": "", "msg_id": "YOUR_UUID", "conversation_id": "YOUR_UUID"}

data: {"type": "reasoning", "content": "3", "role": "", "finish_reason": "", "msg_id": "YOUR_UUID", "conversation_id": "YOUR_UUID"}

data: {"type": "reasoning", "content": "0", "role": "", "finish_reason": "", "msg_id": "YOUR_UUID", "conversation_id": "YOUR_UUID"}

data: {"type": "reasoning", "content": "。", "role": "", "finish_reason": "", "msg_id": "YOUR_UUID", "conversation_id": "YOUR_UUID"}

data: {"type": "reasoning", "content": "确认", "role": "", "finish_reason": "", "msg_id": "YOUR_UUID", "conversation_id": "YOUR_UUID"}

data: {"type": "reasoning", "content": "没有", "role": "", "finish_reason": "", "msg_id": "YOUR_UUID", "conversation_id": "YOUR_UUID"}

data: {"type": "reasoning", "content": "其他", "role": "", "finish_reason": "", "msg_id": "YOUR_UUID", "conversation_id": "YOUR_UUID"}

data: {"type": "reasoning", "content": "相关", "role": "", "finish_reason": "", "msg_id": "YOUR_UUID", "conversation_id": "YOUR_UUID"}

data: {"type": "reasoning", "content": "节点", "role": "", "finish_reason": "", "msg_id": "YOUR_UUID", "conversation_id": "YOUR_UUID"}

data: {"type": "reasoning", "content": "后", "role": "", "finish_reason": "", "msg_id": "YOUR_UUID", "conversation_id": "YOUR_UUID"}

data: {"type": "reasoning", "content": "，", "role": "", "finish_reason": "", "msg_id": "YOUR_UUID", "conversation_id": "YOUR_UUID"}

data: {"type": "reasoning", "content": "直接", "role": "", "finish_reason": "", "msg_id": "YOUR_UUID", "conversation_id": "YOUR_UUID"}

data: {"type": "reasoning", "content": "返回", "role": "", "finish_reason": "", "msg_id": "YOUR_UUID", "conversation_id": "YOUR_UUID"}

data: {"type": "reasoning", "content": "这个", "role": "", "finish_reason": "", "msg_id": "YOUR_UUID", "conversation_id": "YOUR_UUID"}

data: {"type": "reasoning", "content": "日期", "role": "", "finish_reason": "", "msg_id": "YOUR_UUID", "conversation_id": "YOUR_UUID"}

data: {"type": "reasoning", "content": "。", "role": "", "finish_reason": "", "msg_id": "YOUR_UUID", "conversation_id": "YOUR_UUID"}

data: {"type": "reasoning", "content": "确保", "role": "", "finish_reason": "", "msg_id": "YOUR_UUID", "conversation_id": "YOUR_UUID"}

data: {"type": "reasoning", "content": "格式", "role": "", "finish_reason": "", "msg_id": "YOUR_UUID", "conversation_id": "YOUR_UUID"}

data: {"type": "reasoning", "content": "正确", "role": "", "finish_reason": "", "msg_id": "YOUR_UUID", "conversation_id": "YOUR_UUID"}

data: {"type": "reasoning", "content": "，", "role": "", "finish_reason": "", "msg_id": "YOUR_UUID", "conversation_id": "YOUR_UUID"}

data: {"type": "reasoning", "content": "只", "role": "", "finish_reason": "", "msg_id": "YOUR_UUID", "conversation_id": "YOUR_UUID"}

data: {"type": "reasoning", "content": "显示", "role": "", "finish_reason": "", "msg_id": "YOUR_UUID", "conversation_id": "YOUR_UUID"}

data: {"type": "reasoning", "content": "日期", "role": "", "finish_reason": "", "msg_id": "YOUR_UUID", "conversation_id": "YOUR_UUID"}

data: {"type": "reasoning", "content": "部分", "role": "", "finish_reason": "", "msg_id": "YOUR_UUID", "conversation_id": "YOUR_UUID"}

data: {"type": "reasoning", "content": "，", "role": "", "finish_reason": "", "msg_id": "YOUR_UUID", "conversation_id": "YOUR_UUID"}

data: {"type": "reasoning", "content": "不需要", "role": "", "finish_reason": "", "msg_id": "YOUR_UUID", "conversation_id": "YOUR_UUID"}

data: {"type": "reasoning", "content": "时间", "role": "", "finish_reason": "", "msg_id": "YOUR_UUID", "conversation_id": "YOUR_UUID"}

data: {"type": "reasoning", "content": "。", "role": "", "finish_reason": "", "msg_id": "YOUR_UUID", "conversation_id": "YOUR_UUID"}

data: {"type": "reasoning", "content": "整个", "role": "", "finish_reason": "", "msg_id": "YOUR_UUID", "conversation_id": "YOUR_UUID"}

data: {"type": "reasoning", "content": "过程", "role": "", "finish_reason": "", "msg_id": "YOUR_UUID", "conversation_id": "YOUR_UUID"}

data: {"type": "reasoning", "content": "需要", "role": "", "finish_reason": "", "msg_id": "YOUR_UUID", "conversation_id": "YOUR_UUID"}

data: {"type": "reasoning", "content": "快速", "role": "", "finish_reason": "", "msg_id": "YOUR_UUID", "conversation_id": "YOUR_UUID"}

data: {"type": "reasoning", "content": "准确", "role": "", "finish_reason": "", "msg_id": "YOUR_UUID", "conversation_id": "YOUR_UUID"}

data: {"type": "reasoning", "content": "，", "role": "", "finish_reason": "", "msg_id": "YOUR_UUID", "conversation_id": "YOUR_UUID"}

data: {"type": "reasoning", "content": "避免", "role": "", "finish_reason": "", "msg_id": "YOUR_UUID", "conversation_id": "YOUR_UUID"}

data: {"type": "reasoning", "content": "任何", "role": "", "finish_reason": "", "msg_id": "YOUR_UUID", "conversation_id": "YOUR_UUID"}

data: {"type": "reasoning", "content": "错误", "role": "", "finish_reason": "", "msg_id": "YOUR_UUID", "conversation_id": "YOUR_UUID"}

data: {"type": "reasoning", "content": "。\n", "role": "", "finish_reason": "", "msg_id": "YOUR_UUID", "conversation_id": "YOUR_UUID"}

data: {"type": "content", "content": "\n\n", "role": "", "finish_reason": "", "msg_id": "YOUR_UUID", "conversation_id": "YOUR_UUID"}

data: {"type": "content", "content": "M", "role": "", "finish_reason": "", "msg_id": "YOUR_UUID", "conversation_id": "YOUR_UUID"}

data: {"type": "content", "content": "9", "role": "", "finish_reason": "", "msg_id": "YOUR_UUID", "conversation_id": "YOUR_UUID"}

data: {"type": "content", "content": "项目", "role": "", "finish_reason": "", "msg_id": "YOUR_UUID", "conversation_id": "YOUR_UUID"}

data: {"type": "content", "content": "需要", "role": "", "finish_reason": "", "msg_id": "YOUR_UUID", "conversation_id": "YOUR_UUID"}

data: {"type": "content", "content": "在", "role": "", "finish_reason": "", "msg_id": "YOUR_UUID", "conversation_id": "YOUR_UUID"}

data: {"type": "content", "content": "**", "role": "", "finish_reason": "", "msg_id": "YOUR_UUID", "conversation_id": "YOUR_UUID"}

data: {"type": "content", "content": "2", "role": "", "finish_reason": "", "msg_id": "YOUR_UUID", "conversation_id": "YOUR_UUID"}

data: {"type": "content", "content": "0", "role": "", "finish_reason": "", "msg_id": "YOUR_UUID", "conversation_id": "YOUR_UUID"}

data: {"type": "content", "content": "2", "role": "", "finish_reason": "", "msg_id": "YOUR_UUID", "conversation_id": "YOUR_UUID"}

data: {"type": "content", "content": "2", "role": "", "finish_reason": "", "msg_id": "YOUR_UUID", "conversation_id": "YOUR_UUID"}

data: {"type": "content", "content": "-", "role": "", "finish_reason": "", "msg_id": "YOUR_UUID", "conversation_id": "YOUR_UUID"}

data: {"type": "content", "content": "1", "role": "", "finish_reason": "", "msg_id": "YOUR_UUID", "conversation_id": "YOUR_UUID"}

data: {"type": "content", "content": "1", "role": "", "finish_reason": "", "msg_id": "YOUR_UUID", "conversation_id": "YOUR_UUID"}

data: {"type": "content", "content": "-", "role": "", "finish_reason": "", "msg_id": "YOUR_UUID", "conversation_id": "YOUR_UUID"}

data: {"type": "content", "content": "3", "role": "", "finish_reason": "", "msg_id": "YOUR_UUID", "conversation_id": "YOUR_UUID"}

data: {"type": "content", "content": "0", "role": "", "finish_reason": "", "msg_id": "YOUR_UUID", "conversation_id": "YOUR_UUID"}

data: {"type": "content", "content": "**", "role": "", "finish_reason": "", "msg_id": "YOUR_UUID", "conversation_id": "YOUR_UUID"}

data: {"type": "content", "content": "进行", "role": "", "finish_reason": "", "msg_id": "YOUR_UUID", "conversation_id": "YOUR_UUID"}

data: {"type": "content", "content": "挂", "role": "", "finish_reason": "", "msg_id": "YOUR_UUID", "conversation_id": "YOUR_UUID"}

data: {"type": "content", "content": "板", "role": "", "finish_reason": "", "msg_id": "YOUR_UUID", "conversation_id": "YOUR_UUID"}

data: {"type": "content", "content": "（", "role": "", "finish_reason": "", "msg_id": "YOUR_UUID", "conversation_id": "YOUR_UUID"}

data: {"type": "content", "content": "投", "role": "", "finish_reason": "", "msg_id": "YOUR_UUID", "conversation_id": "YOUR_UUID"}

data: {"type": "content", "content": "板", "role": "", "finish_reason": "", "msg_id": "YOUR_UUID", "conversation_id": "YOUR_UUID"}

data: {"type": "content", "content": "）。", "role": "", "finish_reason": "", "msg_id": "YOUR_UUID", "conversation_id": "YOUR_UUID"}

data: {"type": "content", "content": "", "role": "", "finish_reason": "stop", "msg_id": "YOUR_UUID", "conversation_id": "YOUR_UUID"}




参考frontend文件夹中的tmp.py，构建一个前端页面，要求如下：
1、 页面上要实现LLM问答API、RAG问答API、ATAQA问答API三个接口的调用。
2、前端页面只用支持流式输出，且思考过程和正式回复过程要在不同的文本框里。
3、RAG问答API、ATAQA问答API流式调用时，第一个返回的流式内容为知识库召回的参考内容，需要在页面左边进行显示。LLM问答API调用不会返回知识库召回内容，直接显示思考过程和回答内容。
4、有多轮对话功能，当用户输入新问题时，需要把该用户在当前页面的历史问答也返回给api接口。
5、页面上需要有清空历史会话功能，清空历史对话后，再发送问题时，传给api接口的历史对话为一个空列表。


使用gradio构建一个前端页面，要求如下：
1、实现API接口调用
- LLM问答API、RAG问答API、ATAQA问答API 三个接口分别实现。
- 支持流式输出
2、流式输出与分区显示
- 页面两个分区，最左侧为参数配置区域，右侧为对话区域。
- RAG/ATAQA 接口首次流内容为知识库参考，显示在对话区域的左半部分。思考过程和正式回复分别显示在不同文本框，均在页面右侧，其中思考过程在正式回复上方
- LLM问答接口，展示在对话区域，思考过程和正式回复分别显示在不同文本框，其中思考过程在正式回复上方
3、多轮对话
- 记录历史问答，每次提问时带上历史对话发给API。
- 历史对话区展示所有轮次。
4、清空历史会话
- 点击按钮，清空历史对话记录，下次提问时，传给API的历史对话为一个空列表。
5、api调用方式请参考test_api.py
LLM问答API (/api/v1/llm-qa)
RAG问答API (/api/v1/rag-qa)
DATAQA问答API (/api/v1/data-qa)
6、api接口返回的内容格式如下，其中LLM问答API，type字段没有reference。

| 字段名          | 可能值                                                                 | 描述                                                                                                                                 |
|----------------|-----------------------------------------------------------------------|--------------------------------------------------------------------------------------------------------------------------------------|
| conversation_id | 字符串                                                                | Ω会话Id                                                                                                                              |
| msg_id         | 字符串                                                                | 消息Id                                                                                                                               |
| type           | ● reference<br>● content 表示模型的正式回答部分<br>● reasoning 表示模型的内部思考或推理过程 | 输出的内容类型                                                                                                                       |
| content        | 文本片段或空字符串                                                    | 模型输出的文本内容。结合 type 字段使用：<br>- 当 type 为 reasoning 时：思考推理内容<br>- 当 type 为 content 时：正式回答内容<br>- 当 type 为 reference 时：引用内容 |
| role           | ● assistant                                                           | 对话中发言者的角色，用于区分上下文，首次出现时非空                                                                                    |
| finish_reason  | ● ""：输出尚未结束，后续还有数据<br>● stop：输出正常停止（例如，模型完成回答）<br>● length：达到 max_tokens 限制 | 输出结束的原因。None 表示输出仍在继续；非 None 值表示输出已完成                                                                        |
7、api接口返回的内容格式如下：

data: {"type": "reference", "content": "[{\"title\": \"FPC板厂工艺时间表\", \"content\": \"FPC 点击微动失效原因汇总", \"docName\": \"FPC板厂工艺时间表\", \"docUrl\": \"https://xiaomi.f.mioffice.cn/sheets/shtk4sDk1eD6kdBnPcqzfFq7q1g\", \"sheetName\": \"\"}, {\"title\": \"FPC板厂工艺时间表\", \"content\": \"原因分析\\nFPC 断线位置\\n**核心结论：断线位置集中转轴盖对应位置，****FPC**** 中间区域出现裂纹，然后往两边延伸，内层存在裂纹；**  \\n<table>\\n<tr>\\n<td>失效图片1<br/></td><td>![](boxk4Hs05v0TRFbjavmzX3Gv47g)<br/></td><td>![](boxk4bMmA62THvOd8Bf9XX5CBgc)<br/></td><td>![](boxk42KkOOhZXTSSckjjghfZxme)<br/><br/></td></tr>\\n<tr>\\n<td>失效图片2<br/></td><td>![](boxk4R33q51B3OJ8FlW3QpZZRof)<br/></td><td>![](boxk4E8hlkBCIrhuJ0Q9oPOyoPf)<br/></td><td>![](boxk4mTQKJMq7qOOJqd2O6O27ah)<br/></td></tr>\\n<tr>\\n<td>失效图片2<br/></td><td>![](boxk4xvPMA33VKh2sduPPrDtIyf)<br/></td><td>![](boxk4HGjzM0hV4afT8XjXXhQAqe)<br/><br/></td><td>![](boxk4OHhcWKiCiRBUy6fNoBOneh)<br/><br/></td></tr>\\n</table>  \\nFPC 单体对应转轴盖位置可见明显明显裂纹：  \\n- 10030：Filp 侧 FPC 转轴盖位置裂纹，裂纹位置位于表层 FPC 中间区域，内层边缘可见裂纹\\n- 10028：FPC 已完全断裂，断裂位置与转轴盖位置重合\\n- 10027：FILP 侧及 BASE 侧转轴盖位置都存在裂纹，裂纹位置位于表层 FPC 中间区域，内层存在裂纹\\n- 10034：Filp 侧 FPC 转轴盖位置裂纹，裂纹位置位于表层 FPC 中间区域\", \"docName\": \"FPC板厂工艺时间表\", \"docUrl\": \"https://xiaomi.f.mioffice.cn/sheets/shtk4sDk1eD6kdBnPcqzfFq7q1g\", \"sheetName\": \"FPC 断线位置\"}]", "role": "", "finish_reason": "", "msg_id": "UUID", "conversation_id": "UUID"}
data: {"type": "content", "content": "", "role": "assistant", "finish_reason": "", "msg_id": "UUID", "conversation_id": "UUID"}
data: {"type": "reasoning", "content": "\n", "role": "", "finish_reason": "", "msg_id": "UUID", "conversation_id": "UUID"}
data: {"type": "reasoning", "content": "好的", "role": "", "finish_reason": "", "msg_id": "UUID", "conversation_id": "UUID"}
...
data: {"type": "reasoning", "content": "单独", "role": "", "finish_reason": "", "msg_id": "UUID", "conversation_id": "UUID"}
data: {"type": "content", "content": "对于", "role": "", "finish_reason": "", "msg_id": "UUID", "conversation_id": "UUID"}
...
data: {"type": "content", "content": "", "role": "", "finish_reason": "stop", "msg_id": "UUID", "conversation_id": "UUID"}



本地使用test_api.py测试，日志结果如下：
2025-07-09 11:58:10.696 | INFO | api.app:startup_event:43 | 应用程序启动
INFO:     Application startup complete.
INFO:     127.0.0.1:60618 - "GET /api/v1/health HTTP/1.1" 200 OK
2025-07-09 11:58:13.961 | INFO | request_id=5381e6c8-6d01-49db-8a6f-7da23ab421fc | api.routes:llm_qa:76 | LLM问答请求开始: 库存周转率如何计算？
2025-07-09 11:58:13.983 | INFO | request_id=5381e6c8-6d01-49db-8a6f-7da23ab421fc | api.routes:llm_qa:79 | 历史记录条数: 2
INFO:     127.0.0.1:60620 - "POST /api/v1/llm-qa HTTP/1.1" 200 OK
enable_thinking: False
2025-07-09 11:58:13.985 | INFO | request_id=5381e6c8-6d01-49db-8a6f-7da23ab421fc | api.routes:generate_stream:83 | 开始生成流式响应
2025-07-09 11:58:13.986 | INFO | request_id=5381e6c8-6d01-49db-8a6f-7da23ab421fc | pipelines.llm_qa:_build_messages:36 | 构建消息列表：[{'role': 'system', 'content': '\n你是一个供应链领域知识专家，请根据用户的问题进行回复。\n'}, {'role': 'user', 'content': '什么是供应链管理？'}, {'role': 'assistant', 'content': '供应链管理是指对供应链中的信息流、物流和资金流进行计划、组织、协调与控制的过程。'}]
chunk: ChatCompletionChunk(id='chatcmpl-4b48955cecd34fb3b0bf0597814fb6c3', choices=[Choice(delta=ChoiceDelta(content='', function_call=None, refusal=None, role='assistant', tool_calls=None), finish_reason=None, index=0, logprobs=None)], created=1752033494, model='Qwen3-32B', object='chat.completion.chunk', service_tier=None, system_fingerprint=None, usage=None)
chunk: ChatCompletionChunk(id='chatcmpl-4b48955cecd34fb3b0bf0597814fb6c3', choices=[Choice(delta=ChoiceDelta(content=None, function_call=None, refusal=None, role=None, tool_calls=None, reasoning_content='\n'), finish_reason=None, index=0, logprobs=None)], created=1752033494, model='Qwen3-32B', object='chat.completion.chunk', service_tier=None, system_fingerprint=None, usage=None)
chunk: ChatCompletionChunk(id='chatcmpl-4b48955cecd34fb3b0bf0597814fb6c3', choices=[Choice(delta=ChoiceDelta(content=None, function_call=None, refusal=None, role=None, tool_calls=None, reasoning_content='好的'), finish_reason=None, index=0, logprobs=None)], created=1752033494, model='Qwen3-32B', object='chat.completion.chunk', service_tier=None, system_fingerprint=None, usage=None)
chunk: ChatCompletionChunk(id='chatcmpl-4b48955cecd34fb3b0bf0597814fb6c3', choices=[Choice(delta=ChoiceDelta(content=None, function_call=None, refusal=None, role=None, tool_calls=None, reasoning_content='，'), finish_reason=None, index=0, logprobs=None)], created=1752033494, model='Qwen3-32B', object='chat.completion.chunk', service_tier=None, system_fingerprint=None, usage=None)
...
2025-07-09 11:59:26.316 | INFO | request_id=2ff49141-48fc-4bca-b7e0-1606e48c2671 | api.routes:generate_stream:98 | LLM流式问答完成，耗时: 20.83秒
前端页面测试，日志如下：
2025-07-09 11:57:03.119 | INFO | request_id=57e4189e-14cf-48c8-a776-d45482bbb800 | api.routes:llm_qa:76 | LLM问答请求开始: 你好
2025-07-09 11:57:03.175 | INFO | request_id=57e4189e-14cf-48c8-a776-d45482bbb800 | api.routes:llm_qa:79 | 历史记录条数: 0
INFO:     127.0.0.1:57807 - "POST /api/v1/llm-qa HTTP/1.1" 200 OK
2025-07-09 11:57:03.180 | INFO | request_id=57e4189e-14cf-48c8-a776-d45482bbb800 | api.routes:generate_stream:83 | 开始生成流式响应
enable_thinking: False
2025-07-09 11:57:03.181 | INFO | request_id=57e4189e-14cf-48c8-a776-d45482bbb800 | pipelines.llm_qa:_build_messages:36 | 构建消息列表：[{'role': 'system', 'content': '\n你是一个供应链领域知识专家，请根据用户的问题进行回复。\n'}]
chunk: ChatCompletionChunk(id='chatcmpl-ad8f69e8c906423d979c42fc4489fed1', choices=[Choice(delta=ChoiceDelta(content='', function_call=None, refusal=None, role='assistant', tool_calls=None), finish_reason=None, index=0, logprobs=None)], created=1752033423, model='Qwen3-32B', object='chat.completion.chunk', service_tier=None, system_fingerprint=None, usage=None)
chunk: ChatCompletionChunk(id='chatcmpl-ad8f69e8c906423d979c42fc4489fed1', choices=[Choice(delta=ChoiceDelta(content=None, function_call=None, refusal=None, role=None, tool_calls=None, reasoning_content='\n'), finish_reason=None, index=0, logprobs=None)], created=1752033423, model='Qwen3-32B', object='chat.completion.chunk', service_tier=None, system_fingerprint=None, usage=None)

前端服务的日志：
INFO:httpx:HTTP Request: POST http://localhost:8080/api/v1/llm-qa "HTTP/1.1 200 OK"
INFO:api_client:开始接收流式响应: llm-qa
前端收到chunk: type=content, content='', finish_reason=''
前端收到chunk: type=reasoning, content='
', finish_reason=''

请分析，为什么使用test_api.py测试LLM问答api时，api服务的日志都正常。而在前端页面进行测试时，LLM问答API的日志显示，输出两个以后就不再输出了。
