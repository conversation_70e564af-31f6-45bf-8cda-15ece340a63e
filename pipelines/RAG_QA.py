from typing import List, Dict, Any, AsyncGenerator, Optional
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from core.llm_provider import get_llm_provider
from prompts.rag_qa_prompt import rag_qa_sys_prompt, rag_qa_user_prompt
from services.search_service import SearchService
from services.rerank_service import RerankService

from loguru import logger
from config.logging_config import configure_logging
configure_logging()
import json


class RAGQA:
    """RAG问答类，支持流式和非流式输出"""
    
    def __init__(self, model_id: str, request_id: str = None):
        """
        初始化RAG问答实例
        
        Args:
            model_id: 模型ID (e.g. "gpt_4o", "qwen3_32b")
            request_id: 可选请求ID
        """
        self.model_id = model_id
        self.request_id = request_id
        # 实例化模型提供者
        self.provider = get_llm_provider(model_id, request_id)
        # 实例化搜索服务
        self.search_service = SearchService(request_id=request_id)
        # 实例化重排服务
        self.rerank_service = RerankService(request_id=request_id)
        self.logger = logger.bind(request_id=request_id)
        self.logger.info(f"RAGQA instance initialized with model_id: {model_id}")
    
    async def _retrieve_knowledge(self, query: str, user_id: str, top_k: int = 20):
        """检索知识并进行重排"""
        # 搜索知识
        self.logger.info(f"开始检索知识, 用户ID: {user_id}, 查询: {query}, top_k: {top_k}")
        search_results, error = await self.search_service.search(
            user_id=user_id,
            query=query,
            top_k=top_k
        )
        
        if error or not search_results:
            return "", []
        self.logger.info(f"检索到的知识共: {len(search_results)}条")
        # 重排知识
        reranked_docs = await self.rerank_service.rerank(
            query=query,
            documents=search_results
        )
        self.logger.info(f"重排后的知识共: {len(reranked_docs)}条")
        # print(f"重排后的知识: {reranked_docs}")
        # 格式化重排后的知识为字符串
        formatted_docs = []
        for i, doc in enumerate(reranked_docs):
            formatted_doc = f"文档{i+1}:\n"
            formatted_doc += f"标题: {doc.get('title', '')}\n"
            formatted_doc += f"内容: {doc.get('content', '')}\n"
            formatted_doc += f"文档名称: {doc.get('docName', '')}\n"
            formatted_doc += f"章节名称: {doc.get('sheetName', '')}\n"
            formatted_doc += f"文档链接: {doc.get('docUrl', '')}\n"
            formatted_docs.append(formatted_doc)
        self.logger.info(f"格式化后的知识, 共: {len(formatted_docs)}条")
        return "\n\n".join(formatted_docs), reranked_docs
    
    def _build_messages(self, query: str, history: List[Dict], knowledge: str) -> List[Dict]:
        """构建OpenAI格式的消息列表（适配新history格式）"""
        messages = [{"role": "system", "content": rag_qa_sys_prompt}]
        for item in history[::-1]:
            messages.append({"role": "user", "content": item["query"]})
            messages.append({"role": "assistant", "content": item["content"]})
        formatted_query = rag_qa_user_prompt.replace("{{query}}", query).replace("{{body}}", knowledge)
        messages.append({"role": "user", "content": formatted_query})
        return messages
    
    async def generate(
        self, 
        query: str, 
        user_id: str, 
        history: List[Dict] = None,
        timeout: Optional[float] = None,
        conversation_id: Optional[str] = None,
        enable_thinking: bool = False,
        **kwargs
    ) -> Dict[str, Any]:
        """
        非流式RAG问答生成
        """
        if history is None:
            history = []
            
        # 检索知识
        knowledge, rerank_res = await self._retrieve_knowledge(query, user_id)
        self.logger.info(f"知识检索完成")
        
        # 构建消息
        messages = self._build_messages(query, history, knowledge)
        
        # 调用模型生成回答
        return await self.provider.generate(
            messages=messages,
            timeout=timeout,
            conversation_id=conversation_id,
            enable_thinking=enable_thinking,
            **kwargs
        )
    
    async def generate_stream(
        self, 
        query: str, 
        user_id: str, 
        history: List[Dict] = None,
        timeout: Optional[float] = None,
        top_k: int = 20,
        conversation_id: Optional[str] = None,
        enable_thinking: bool = False,
        **kwargs
    ) -> AsyncGenerator[Dict[str, Any], None]:
        """
        流式RAG问答生成
        """
        if history is None:
            self.logger.info(f"历史对话为空，开始新对话")
            history = []
        self.logger.info(f"历史对话条数: {len(history)}")
        # 检索知识
        knowledge, rerank_res = await self._retrieve_knowledge(query, user_id, top_k=top_k)
        self.logger.info(f"知识检索完成")
        # print(f"检索到的知识：{rerank_res}")
        
        yield {"type": "reference", "content": json.dumps(rerank_res, ensure_ascii=False), "role": "", "finish_reason": ""}
        
        rerank_res_str =  json.dumps(rerank_res, ensure_ascii=False)
        self.logger.info(f"知识检索结果格式化处理")
        # 构建消息
        messages = self._build_messages(query, history, rerank_res_str)
        self.logger.info(f"消息构建完成")
        print(f"构建消息：{messages}")

        # 模型服务的流式调用
        async for chunk in self.provider.generate_stream(
            messages=messages,
            timeout=timeout,
            conversation_id=conversation_id,
            enable_thinking=enable_thinking,
            **kwargs
        ):
            yield chunk