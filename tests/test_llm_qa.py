


import sys
import os
import asyncio

sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# 添加环境变量加载代码
from dotenv import load_dotenv

# 获取环境变量中的环境类型，默认为local
env_type = os.environ.get("ENVIRONMENT", "local")
env_file = f".env.{env_type}"

# 尝试加载对应环境的配置文件
env_path = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), env_file)
if os.path.exists(env_path):
    load_dotenv(env_path)
    print(f"已加载{env_type}环境配置: {env_path}")
else:
    # 如果特定环境配置不存在，尝试加载默认.env文件
    default_env_path = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), '.env')
    if os.path.exists(default_env_path):
        load_dotenv(default_env_path)
        print(f"已加载默认环境配置: {default_env_path}")
    else:
        print(f"警告: 环境配置文件不存在: {env_path} 或 {default_env_path}")


from pipelines.llm_qa import LLMQA

async def main():
    
    # # 初始化问答实例
    qa = LLMQA(model_id="qwen3_32b", request_id="req_123")
    query="库存周转率如何计算？",
    user_id="user_abc",
    history=[{"role": "user", "content": "什么是供应链管理？"}, 
            {"role": "assistant", "content": "供应链管理是..."}]

    # 非流式调用
    # result = await qa.generate(query, user_id, history)
    # print(f"非流式调用结果: {result}")
    
    # 流式调用
    async for chunk in qa.generate_stream(query, user_id, history):
        print(chunk)
        
if __name__ == "__main__":
    asyncio.run(main())