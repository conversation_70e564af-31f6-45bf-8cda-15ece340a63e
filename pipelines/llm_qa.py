from typing import List, Dict, Any, AsyncGenerator, Optional
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from core.llm_provider import get_llm_provider
from prompts.llm_qa_prompt import system_prompt, user_query

from loguru import logger
from config.logging_config import configure_logging
configure_logging()

class LLMQA:
    """LLM问答基础类，支持流式和非流式输出"""
    
    def __init__(self, model_id: str, request_id: str = None):
        """
        初始化问答实例
        
        Args:
            model_id: 模型ID (e.g. "gpt_4o", "qwen3_32b")
            request_id: 可选请求ID
        """
        self.model_id = model_id
        self.request_id = request_id
        # 实例化模型提供者
        self.provider = get_llm_provider(model_id, request_id)
        self.logger = logger.bind(request_id=request_id)
    
    def _build_messages(self, query: str, history: List[Dict]) -> List[Dict]:
        """构建OpenAI格式的消息列表"""
        messages = [{"role": "system", "content": system_prompt}]
        
        # 添加历史对话
        messages.extend(history)
        self.logger.info(f"构建消息列表：{messages}")
        # 添加当前查询（使用模板格式化）
        formatted_query = user_query.format(query=query)
        messages.append({"role": "user", "content": formatted_query})
        
        return messages
    
    async def generate(
        self, 
        query: str, 
        user_id: str, 
        history: List[Dict],
        timeout: Optional[float] = None,
        conversation_id: Optional[str] = None,
        enable_thinking: bool = False,
        **kwargs
    ) -> Dict[str, Any]:
        """
        非流式问答生成
        """
        messages = self._build_messages(query, history)
        return await self.provider.generate(
            messages=messages,
            timeout=timeout,
            conversation_id=conversation_id,
            enable_thinking=enable_thinking,
            **kwargs
        )
    
    async def generate_stream(
        self, 
        query: str, 
        user_id: str, 
        history: List[Dict],
        timeout: Optional[float] = None,
        conversation_id: Optional[str] = None,
        enable_thinking: bool = False,
        **kwargs
    ) -> AsyncGenerator[Dict[str, Any], None]:
        # 模型服务的流式调用
        messages = self._build_messages(query, history)
        async for chunk in self.provider.generate_stream(
            messages=messages,
            timeout=timeout,
            conversation_id=conversation_id,
            enable_thinking=enable_thinking,
            **kwargs
        ):
            yield chunk
                
