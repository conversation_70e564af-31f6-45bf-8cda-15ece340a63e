#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
基于Gradio的前端应用 - 简化版本
实现LLM问答、RAG问答、DATAQA问答的Web界面
"""

import gradio as gr
import asyncio
import json
import uuid
import httpx
import time
from typing import Dict, Any, List, Optional, Tuple
import traceback
from datetime import datetime
import sys
import os

# 导入配置
try:
    from config import (
        API_BASE_URL, DEFAULT_MODELS, DEFAULT_TOP_K, DEFAULT_MODEL,
        THEME, TITLE, DESCRIPTION, REQUEST_TIMEOUT,
        GRADIO_HOST, GRADIO_PORT, GRADIO_SHARE, GRADIO_DEBUG
    )
except ImportError:
    # 如果配置文件不存在，使用默认值
    API_BASE_URL = "http://localhost:8080/api/v1"
    DEFAULT_MODELS = ["qwen3_32b", "gpt-4", "claude-3"]
    DEFAULT_TOP_K = 3
    DEFAULT_MODEL = "qwen3_32b"
    THEME = "soft"
    TITLE = "🤖 智能问答系统"
    DESCRIPTION = "支持LLM问答、RAG问答、DATAQA问答三种模式"
    REQUEST_TIMEOUT = 600.0
    GRADIO_HOST = "0.0.0.0"
    GRADIO_PORT = 7860
    GRADIO_SHARE = False
    GRADIO_DEBUG = True

class ChatApp:
    def __init__(self, api_url: str = API_BASE_URL):
        self.api_url = api_url
        self.conversation_history = []  # 存储历史对话
        
    def clear_history(self):
        """清空历史对话"""
        self.conversation_history = []
        return ""
    
    async def call_api_stream(self, endpoint: str, payload: Dict[str, Any]):
        """调用API流式接口"""
        url = f"{self.api_url}/{endpoint}"
        responses = []
        
        try:
            async with httpx.AsyncClient(timeout=REQUEST_TIMEOUT) as client:
                async with client.stream("POST", url, json=payload) as response:
                    response.raise_for_status()
                    async for line in response.aiter_lines():
                        if line.startswith("data: "):
                            data = line[6:]
                            try:
                                chunk = json.loads(data)
                                responses.append(chunk)
                            except json.JSONDecodeError:
                                continue
        except Exception as e:
            error_msg = f"API调用失败: {str(e)}"
            responses.append({
                "type": "content",
                "content": error_msg,
                "role": "assistant",
                "finish_reason": "error"
            })
        
        return responses
    
    def process_stream_responses(self, responses: List[Dict[str, Any]]) -> Tuple[str, str, str]:
        """处理流式响应，分离reference、reasoning、content"""
        reference_content = ""
        reasoning_content = ""
        content_content = ""
        
        for chunk in responses:
            chunk_type = chunk.get("type", "")
            chunk_content = chunk.get("content", "")
            
            if chunk_type == "reference":
                reference_content += chunk_content
            elif chunk_type == "reasoning":
                reasoning_content += chunk_content
            elif chunk_type == "content":
                content_content += chunk_content
        
        return reference_content, reasoning_content, content_content
    
    def format_reference_display(self, reference_content: str) -> str:
        """格式化参考内容显示"""
        if not reference_content:
            return ""
        
        try:
            # 尝试解析JSON格式的参考内容
            references = json.loads(reference_content)
            if isinstance(references, list):
                formatted = "📚 **知识库参考：**\n\n"
                for i, ref in enumerate(references, 1):
                    title = ref.get("title", "未知标题")
                    content = ref.get("content", "")
                    doc_name = ref.get("docName", "")
                    doc_url = ref.get("docUrl", "")
                    
                    formatted += f"**参考 {i}：{title}**\n"
                    if doc_name:
                        formatted += f"📄 文档：{doc_name}\n"
                    if doc_url:
                        formatted += f"🔗 链接：{doc_url}\n"
                    if content:
                        formatted += f"📝 内容：{content}\n"
                    formatted += "\n---\n\n"
                return formatted
        except:
            pass
        
        return f"📚 **知识库参考：**\n\n{reference_content}"
    
    async def chat_llm_stream(self, query: str, model_id: str, history_display: str):
        """LLM问答 - 流式输出版本"""
        if not query.strip():
            yield history_display, "", ""
            return

        # 准备请求参数
        request_id = str(uuid.uuid4())
        conversation_id = str(uuid.uuid4())

        payload = {
            "query": query,
            "user_id": "gradio_user",
            "model_id": model_id,
            "msg_id": request_id,
            "conversation_id": conversation_id,
            "history": self.conversation_history,
            "stream": True
        }

        # 调用API并实时更新
        reasoning_content = ""
        content_content = ""

        try:
            url = f"{self.api_url}/llm-qa"
            async with httpx.AsyncClient(timeout=REQUEST_TIMEOUT) as client:
                async with client.stream("POST", url, json=payload) as response:
                    response.raise_for_status()
                    async for line in response.aiter_lines():
                        if line.startswith("data: "):
                            data = line[6:]
                            try:
                                chunk = json.loads(data)
                                chunk_type = chunk.get("type", "")
                                chunk_content = chunk.get("content", "")

                                if chunk_type == "reasoning":
                                    reasoning_content += chunk_content
                                    yield history_display, reasoning_content, content_content
                                elif chunk_type == "content":
                                    content_content += chunk_content
                                    yield history_display, reasoning_content, content_content
                            except json.JSONDecodeError:
                                continue
        except Exception as e:
            error_msg = f"API调用失败: {str(e)}"
            content_content = error_msg
            yield history_display, reasoning_content, content_content

        # 更新历史对话
        self.conversation_history.append({"role": "user", "content": query})
        self.conversation_history.append({"role": "assistant", "content": content_content})

        # 更新历史显示
        timestamp = datetime.now().strftime("%H:%M:%S")
        new_history = f"{history_display}\n\n**[{timestamp}] 用户：**\n{query}\n\n**[{timestamp}] 助手：**\n{content_content}"

        yield new_history, reasoning_content, content_content

    async def chat_rag_stream(self, query: str, model_id: str, top_k: int, history_display: str):
        """RAG问答 - 流式输出版本"""
        if not query.strip():
            yield history_display, "", "", ""
            return

        # 准备请求参数
        request_id = str(uuid.uuid4())
        conversation_id = str(uuid.uuid4())

        payload = {
            "query": query,
            "user_id": "gradio_user",
            "model_id": model_id,
            "msg_id": request_id,
            "conversation_id": conversation_id,
            "history": self.conversation_history,
            "stream": True,
            "top_k": top_k
        }

        # 调用API并实时更新
        reference_content = ""
        reasoning_content = ""
        content_content = ""

        try:
            url = f"{self.api_url}/rag-qa"
            async with httpx.AsyncClient(timeout=REQUEST_TIMEOUT) as client:
                async with client.stream("POST", url, json=payload) as response:
                    response.raise_for_status()
                    async for line in response.aiter_lines():
                        if line.startswith("data: "):
                            data = line[6:]
                            try:
                                chunk = json.loads(data)
                                chunk_type = chunk.get("type", "")
                                chunk_content = chunk.get("content", "")

                                if chunk_type == "reference":
                                    reference_content += chunk_content
                                    formatted_reference = self.format_reference_display(reference_content)
                                    yield history_display, formatted_reference, reasoning_content, content_content
                                elif chunk_type == "reasoning":
                                    reasoning_content += chunk_content
                                    formatted_reference = self.format_reference_display(reference_content)
                                    yield history_display, formatted_reference, reasoning_content, content_content
                                elif chunk_type == "content":
                                    content_content += chunk_content
                                    formatted_reference = self.format_reference_display(reference_content)
                                    yield history_display, formatted_reference, reasoning_content, content_content
                            except json.JSONDecodeError:
                                continue
        except Exception as e:
            error_msg = f"API调用失败: {str(e)}"
            content_content = error_msg
            formatted_reference = self.format_reference_display(reference_content)
            yield history_display, formatted_reference, reasoning_content, content_content

        # 更新历史对话
        self.conversation_history.append({"role": "user", "content": query})
        self.conversation_history.append({"role": "assistant", "content": content_content})

        # 更新历史显示
        timestamp = datetime.now().strftime("%H:%M:%S")
        new_history = f"{history_display}\n\n**[{timestamp}] 用户：**\n{query}\n\n**[{timestamp}] 助手：**\n{content_content}"

        formatted_reference = self.format_reference_display(reference_content)
        yield new_history, formatted_reference, reasoning_content, content_content

    async def chat_dataqa_stream(self, query: str, model_id: str, top_k: int, history_display: str):
        """DATAQA问答 - 流式输出版本"""
        if not query.strip():
            yield history_display, "", "", ""
            return

        # 准备请求参数
        request_id = str(uuid.uuid4())
        conversation_id = str(uuid.uuid4())

        payload = {
            "query": query,
            "user_id": "gradio_user",
            "model_id": model_id,
            "msg_id": request_id,
            "conversation_id": conversation_id,
            "history": self.conversation_history,
            "stream": True,
            "top_k": top_k
        }

        # 调用API并实时更新
        reference_content = ""
        reasoning_content = ""
        content_content = ""

        try:
            url = f"{self.api_url}/data-qa"
            async with httpx.AsyncClient(timeout=REQUEST_TIMEOUT) as client:
                async with client.stream("POST", url, json=payload) as response:
                    response.raise_for_status()
                    async for line in response.aiter_lines():
                        if line.startswith("data: "):
                            data = line[6:]
                            try:
                                chunk = json.loads(data)
                                chunk_type = chunk.get("type", "")
                                chunk_content = chunk.get("content", "")

                                if chunk_type == "reference":
                                    reference_content += chunk_content
                                    formatted_reference = self.format_reference_display(reference_content)
                                    yield history_display, formatted_reference, reasoning_content, content_content
                                elif chunk_type == "reasoning":
                                    reasoning_content += chunk_content
                                    formatted_reference = self.format_reference_display(reference_content)
                                    yield history_display, formatted_reference, reasoning_content, content_content
                                elif chunk_type == "content":
                                    content_content += chunk_content
                                    formatted_reference = self.format_reference_display(reference_content)
                                    yield history_display, formatted_reference, reasoning_content, content_content
                            except json.JSONDecodeError:
                                continue
        except Exception as e:
            error_msg = f"API调用失败: {str(e)}"
            content_content = error_msg
            formatted_reference = self.format_reference_display(reference_content)
            yield history_display, formatted_reference, reasoning_content, content_content

        # 更新历史对话
        self.conversation_history.append({"role": "user", "content": query})
        self.conversation_history.append({"role": "assistant", "content": content_content})

        # 更新历史显示
        timestamp = datetime.now().strftime("%H:%M:%S")
        new_history = f"{history_display}\n\n**[{timestamp}] 用户：**\n{query}\n\n**[{timestamp}] 助手：**\n{content_content}"

        formatted_reference = self.format_reference_display(reference_content)
        yield new_history, formatted_reference, reasoning_content, content_content

def create_gradio_interface():
    """创建Gradio界面"""
    app = ChatApp()

    # 自定义CSS样式
    custom_css = """
    .gradio-container {
        font-size: 14px !important;
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    }
    .gr-button {
        font-size: 13px !important;
    }
    .gr-textbox {
        font-size: 13px !important;
    }
    .gr-markdown {
        font-size: 14px !important;
    }
    .gr-markdown h1 {
        font-size: 24px !important;
        margin-bottom: 10px !important;
    }
    .gr-markdown h2 {
        font-size: 18px !important;
        margin-bottom: 8px !important;
    }
    .gr-tab-nav {
        font-size: 14px !important;
    }
    .gr-form {
        gap: 8px !important;
    }
    .gr-panel {
        padding: 12px !important;
    }
    """

    # 包装异步生成器为同步生成器，支持流式输出
    def sync_chat_llm(query, model_id, history_display):
        async def async_gen():
            async for result in app.chat_llm_stream(query, model_id, history_display):
                yield result

        # 运行异步生成器
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        try:
            gen = async_gen()
            while True:
                try:
                    yield loop.run_until_complete(gen.__anext__())
                except StopAsyncIteration:
                    break
        finally:
            loop.close()

    def sync_chat_rag(query, model_id, top_k, history_display):
        async def async_gen():
            async for result in app.chat_rag_stream(query, model_id, top_k, history_display):
                yield result

        # 运行异步生成器
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        try:
            gen = async_gen()
            while True:
                try:
                    yield loop.run_until_complete(gen.__anext__())
                except StopAsyncIteration:
                    break
        finally:
            loop.close()

    def sync_chat_dataqa(query, model_id, top_k, history_display):
        async def async_gen():
            async for result in app.chat_dataqa_stream(query, model_id, top_k, history_display):
                yield result

        # 运行异步生成器
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        try:
            gen = async_gen()
            while True:
                try:
                    yield loop.run_until_complete(gen.__anext__())
                except StopAsyncIteration:
                    break
        finally:
            loop.close()

    with gr.Blocks(title=TITLE, theme=getattr(gr.themes, THEME.capitalize())(), css=custom_css) as interface:
        gr.Markdown(f"# {TITLE}")
        gr.Markdown(DESCRIPTION)

        with gr.Row():
            # 左侧参数配置区域 - 缩小比例
            with gr.Column(scale=1, min_width=200):
                gr.Markdown("### 配置")

                # 模型选择 - 缩小
                model_id = gr.Dropdown(
                    choices=DEFAULT_MODELS,
                    value=DEFAULT_MODEL,
                    label="模型",
                    container=False
                )

                # top_k参数 - 缩小
                top_k = gr.Slider(
                    minimum=1,
                    maximum=10,
                    value=DEFAULT_TOP_K,
                    step=1,
                    label="Top-K",
                    info="检索数量",
                    visible=True,
                    container=False
                )

                # 清空历史按钮 - 缩小
                clear_btn = gr.Button("🗑️ 清空", variant="secondary", size="sm")

            # 右侧主要区域 - 增大比例
            with gr.Column(scale=4):
                # 使用标签页来区分三种API类型
                with gr.Tabs() as tabs:
                    # LLM问答标签页
                    with gr.TabItem("🤖 Qwen模型", id="llm") as llm_tab:
                        gr.Markdown("### 海量知识，快速检索，智能问答")

                        # 输入区域
                        llm_query_input = gr.Textbox(
                            placeholder="一键解锁知识库内容，尽情合意问，体验AI Chat新技能",
                            lines=2,
                            show_label=False
                        )

                        with gr.Row():
                            llm_submit_btn = gr.Button("发送", variant="primary", size="sm")

                        # 对话显示区域
                        with gr.Row():
                            llm_history_display = gr.Textbox(
                                label="对话历史",
                                lines=12,
                                interactive=False,
                                show_copy_button=True
                            )

                        with gr.Row():
                            with gr.Column():
                                llm_reasoning_display = gr.Textbox(
                                    label="🤔 思考过程",
                                    lines=4,
                                    interactive=False,
                                    show_copy_button=True
                                )
                            with gr.Column():
                                llm_content_display = gr.Textbox(
                                    label="✅ 回复内容",
                                    lines=4,
                                    interactive=False,
                                    show_copy_button=True
                                )

                    # RAG问答标签页
                    with gr.TabItem("📚 知识库", id="rag") as rag_tab:
                        gr.Markdown("### 基于知识库的智能问答")

                        # 输入区域
                        rag_query_input = gr.Textbox(
                            placeholder="请输入您的问题，我将基于知识库为您解答",
                            lines=2,
                            show_label=False
                        )

                        with gr.Row():
                            rag_submit_btn = gr.Button("发送", variant="primary", size="sm")

                        # 对话显示区域
                        with gr.Row():
                            with gr.Column():
                                rag_history_display = gr.Textbox(
                                    label="对话历史",
                                    lines=12,
                                    interactive=False,
                                    show_copy_button=True
                                )
                            with gr.Column():
                                rag_reference_display = gr.Textbox(
                                    label="📚 知识库参考",
                                    lines=12,
                                    interactive=False,
                                    show_copy_button=True
                                )

                        with gr.Row():
                            with gr.Column():
                                rag_reasoning_display = gr.Textbox(
                                    label="🤔 思考过程",
                                    lines=4,
                                    interactive=False,
                                    show_copy_button=True
                                )
                            with gr.Column():
                                rag_content_display = gr.Textbox(
                                    label="✅ 回复内容",
                                    lines=4,
                                    interactive=False,
                                    show_copy_button=True
                                )

                    # DATAQA问答标签页
                    with gr.TabItem("📊 合问答", id="dataqa") as dataqa_tab:
                        gr.Markdown("### 数据问答系统")

                        # 输入区域
                        dataqa_query_input = gr.Textbox(
                            placeholder="请输入数据相关问题，我将为您分析解答",
                            lines=2,
                            show_label=False
                        )

                        with gr.Row():
                            dataqa_submit_btn = gr.Button("发送", variant="primary", size="sm")

                        # 对话显示区域
                        with gr.Row():
                            with gr.Column():
                                dataqa_history_display = gr.Textbox(
                                    label="对话历史",
                                    lines=12,
                                    interactive=False,
                                    show_copy_button=True
                                )
                            with gr.Column():
                                dataqa_reference_display = gr.Textbox(
                                    label="📊 数据参考",
                                    lines=12,
                                    interactive=False,
                                    show_copy_button=True
                                )

                        with gr.Row():
                            with gr.Column():
                                dataqa_reasoning_display = gr.Textbox(
                                    label="🤔 思考过程",
                                    lines=4,
                                    interactive=False,
                                    show_copy_button=True
                                )
                            with gr.Column():
                                dataqa_content_display = gr.Textbox(
                                    label="✅ 回复内容",
                                    lines=4,
                                    interactive=False,
                                    show_copy_button=True
                                )

        # 标签页切换时显示/隐藏top_k参数
        def update_top_k_visibility(selected_tab):
            if selected_tab in [1, 2]:  # RAG和DATAQA标签页
                return gr.update(visible=True)
            else:
                return gr.update(visible=False)

        # 注释掉tabs.select，因为Tabs组件不能作为输入
        # tabs.select(
        #     fn=update_top_k_visibility,
        #     inputs=[tabs],
        #     outputs=[top_k]
        # )

        # LLM问答事件处理
        llm_submit_btn.click(
            fn=sync_chat_llm,
            inputs=[llm_query_input, model_id, llm_history_display],
            outputs=[llm_history_display, llm_reasoning_display, llm_content_display]
        ).then(
            fn=lambda: "",  # 清空输入框
            outputs=[llm_query_input]
        )

        llm_query_input.submit(
            fn=sync_chat_llm,
            inputs=[llm_query_input, model_id, llm_history_display],
            outputs=[llm_history_display, llm_reasoning_display, llm_content_display]
        ).then(
            fn=lambda: "",  # 清空输入框
            outputs=[llm_query_input]
        )

        # RAG问答事件处理
        rag_submit_btn.click(
            fn=sync_chat_rag,
            inputs=[rag_query_input, model_id, top_k, rag_history_display],
            outputs=[rag_history_display, rag_reference_display, rag_reasoning_display, rag_content_display]
        ).then(
            fn=lambda: "",  # 清空输入框
            outputs=[rag_query_input]
        )

        rag_query_input.submit(
            fn=sync_chat_rag,
            inputs=[rag_query_input, model_id, top_k, rag_history_display],
            outputs=[rag_history_display, rag_reference_display, rag_reasoning_display, rag_content_display]
        ).then(
            fn=lambda: "",  # 清空输入框
            outputs=[rag_query_input]
        )

        # DATAQA问答事件处理
        dataqa_submit_btn.click(
            fn=sync_chat_dataqa,
            inputs=[dataqa_query_input, model_id, top_k, dataqa_history_display],
            outputs=[dataqa_history_display, dataqa_reference_display, dataqa_reasoning_display, dataqa_content_display]
        ).then(
            fn=lambda: "",  # 清空输入框
            outputs=[dataqa_query_input]
        )

        dataqa_query_input.submit(
            fn=sync_chat_dataqa,
            inputs=[dataqa_query_input, model_id, top_k, dataqa_history_display],
            outputs=[dataqa_history_display, dataqa_reference_display, dataqa_reasoning_display, dataqa_content_display]
        ).then(
            fn=lambda: "",  # 清空输入框
            outputs=[dataqa_query_input]
        )

        # 清空历史事件 - 清空所有标签页的内容
        def clear_all_history():
            app.clear_history()
            return ("", "", "", "", "", "", "", "", "", "", "", "", "")

        clear_btn.click(
            fn=clear_all_history,
            outputs=[
                llm_history_display, llm_reasoning_display, llm_content_display, llm_query_input,
                rag_history_display, rag_reference_display, rag_reasoning_display, rag_content_display, rag_query_input,
                dataqa_history_display, dataqa_reference_display, dataqa_reasoning_display, dataqa_content_display, dataqa_query_input
            ]
        )

    return interface

if __name__ == "__main__":
    # 创建并启动界面
    interface = create_gradio_interface()
    interface.launch(
        server_name=GRADIO_HOST,
        server_port=GRADIO_PORT,
        share=GRADIO_SHARE,
        debug=GRADIO_DEBUG
    )
