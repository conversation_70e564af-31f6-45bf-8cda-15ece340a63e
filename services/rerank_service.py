import httpx
import logging
import json
from config.rerank_config import RERANK_MODEL_CONFIG
from prompts.rag_qa_prompt import rerank_prompt
from typing import List, Any
from loguru import logger
from config.logging_config import configure_logging
configure_logging()

class RerankService:
    def __init__(self, config=None, request_id: str = None):
        self.config = config or RERANK_MODEL_CONFIG
        self.api_url = self.config["api_url"]
        self.api_key = self.config["api_key"]
        self.default_params = self.config.get("default_params", {})
        self.top_k = self.config.get("top_k", 20)
        self.min_score = self.config.get("min_score", 0.5)
        self.logger = logger.bind(request_id=request_id)

    async def rerank(self, query, documents, top_k=None, min_score=None):
        if not documents:
            return []
        payload = {
            "queries": [query] * len(documents),
            "documents": [doc.get("content", "") for doc in documents],
            "instruction": rerank_prompt
        }

        headers = {
            "api_key": self.api_key,
            "Content-Type": "application/json"
        }
        try:
            async with httpx.AsyncClient() as client:
                response = await client.post(self.api_url, json=payload, headers=headers)
                response.raise_for_status()
                result = response.json()
            self.logger.info(f"Rerank 结果: {result}")
            scores = result.get("scores", [])
            scored_docs = list(zip(scores, documents))
            scored_docs.sort(key=lambda x: x[0], reverse=True)
            top_k = top_k if top_k else self.top_k
            min_score = min_score if min_score is not None else self.min_score
            # print("min_score:", min_score)
            # print("top_k:", top_k)
            if min_score > 0:
                scored_docs = [(score, doc) for score, doc in scored_docs if score >= min_score]
            if top_k is not None and top_k > 0:
                scored_docs = scored_docs[:int(top_k)]
            self.logger.info(f"Score小于{min_score}的文档被过滤掉，共过滤掉 {len(documents) - len(scored_docs)} 个文档")
            self.logger.info(f"Reranker 后处理成功，共获取到 {len(scored_docs)} 个文档")
            # print(f"reranked_retrieved_docs: {scored_docs}")
            reranked_retrieved_docs = [doc for score, doc in scored_docs]
            # print(f"reranked_retrieved_docs: {reranked_retrieved_docs}")
            format_reranked_retrieved_docs = self.format_retrieved_docs(reranked_retrieved_docs)
            self.logger.info(f"Reranker结果格式化完成，共获取到 {len(format_reranked_retrieved_docs)} 个文档")
            # print(f"format_reranked_retrieved_docs: {format_reranked_retrieved_docs}")
            return format_reranked_retrieved_docs
        except Exception as e:
            self.logger.error(f"Reranker API调用失败: {e}")
            return []

    def format_retrieved_docs(self, retrieved_docs: List[Any]) -> List[Any]:
            '''
            input retrieved_docs格式:
            [
                {
                    "content": "PI 仿真项经验总结及设计指导\n环路电感优化思路\n\n时刻关注电流的"返回路径"和"最小合围面积"即环路面积（Loop  Area）,减小环路电感的最直接方式就是减小环路面积  \n!\n!  \n3-1 MTK 环路电感分析  \n减小环路面积的方式：",
                    "metadata": {
                        "Header 1": "PI 仿真项经验总结及设计指导",
                        "Header 2": "环路电感优化思路",
                        "doc_name": "PI仿真项经验总结及设计指导",
                        "doc_type": "doc",
                        "publish_time": "2025-04-23 19:54:26",
                        "project_area": "011",
                        "doc_url": "https://xiaomi.f.mioffice.cn/docx/doxk4Flb2yoMvfV51WSEedury9g",
                        "tm": "2025-05-06 19:23:48",
                        "importance": 90
                    },
                    "relevance_score": 64.73157942295074,
                    "doc_name": "PI仿真项经验总结及设计指导",
                    "doc_url": "https://xiaomi.f.mioffice.cn/docx/doxk4Flb2yoMvfV51WSEedury9g"
                    }
                }
            ]
            输出：
            [
                {
                    title：查询到的标题; 
                    content：查询到的内容;
                    docName：引用的文档名称;
                    sheetName：文档中章节的名称;
                    docUrl：引用的文档链接
                }
            ]
            '''
            formated_retrieved_docs = []
            for doc in retrieved_docs:
                # print(f"doc: {doc}")
                data = {
                    "title": "",
                    "content": "",
                    "docName": "",
                    "docUrl": "",
                    "sheetName": "",
                    # "importance": 
                }
                docUrl = doc.get("metadata_json", {}).get("doc_url", "")
                data.update({"title": doc.get("metadata_json", {}).get("doc_name", "")})
                data.update({"content": doc.get("content", "")})
                data.update({"docName": doc.get("metadata_json", {}).get("doc_name", "")})
                data.update({"docUrl": docUrl})
                docType = doc.get("metadata_json", {}).get("doc_type", "")
                if "xiaomi.f.mioffice.cn"in docUrl and docType in ["doc", "sheet"]:
                    sheetName = doc.get("metadata_json", {}).get("Header 2", "")
                    data.update({"sheetName": sheetName})
                formated_retrieved_docs.append(data)
            return formated_retrieved_docs