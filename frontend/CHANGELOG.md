# 更新日志

## V2.0.0 - 2025-07-09

### 🆕 新增功能

#### 标签页设计
- 将三种API类型重新设计为独立的标签页
- **🤖 Qwen模型**：纯大语言模型问答
- **📚 知识库**：基于知识库的检索增强生成  
- **📊 合问答**：数据问答系统
- 每个标签页拥有独立的输入和显示区域

#### 界面优化
- **缩小字体**：从默认字体缩小到14px，界面更紧凑
- **优化布局**：左侧配置区域，右侧标签页区域
- **自定义CSS**：添加自定义样式，提升视觉效果
- **响应式设计**：更好的屏幕适配

#### 流式输出改进
- 实现真正的流式打印效果
- 思考过程和回复内容实时更新
- 支持异步生成器，提升性能

### 🔧 技术改进

#### 代码结构
- 创建`gradio_app_v2.py`作为新版本主文件
- 简化异步处理逻辑
- 优化事件处理机制

#### 配置管理
- 支持`config.py`配置文件
- 环境变量配置支持
- 默认值回退机制

#### 错误处理
- 修复Tabs组件输入错误
- 改进异常处理逻辑
- 增强稳定性

### 📋 功能对比

| 功能 | V1版本 | V2版本 |
|------|--------|--------|
| API类型选择 | Radio按钮 | 标签页 |
| 界面布局 | 传统分栏 | 标签页+配置区 |
| 字体大小 | 默认 | 缩小(14px) |
| 流式输出 | 基础支持 | 真实流式打印 |
| 参考内容显示 | 条件显示 | 独立区域 |
| 清空功能 | 单一清空 | 全局清空 |

### 🚀 启动方式

#### 方法1：直接运行V2版本
```bash
python3 gradio_app_v2.py
```

#### 方法2：使用启动脚本
```bash
python3 run_gradio.py
```

#### 方法3：演示模式
```bash
python3 demo.py
```

### 📁 文件结构

```
frontend/
├── gradio_app.py          # V1版本（已废弃）
├── gradio_app_v2.py       # V2版本主文件
├── config.py              # 配置文件
├── run_gradio.py          # 启动脚本
├── demo.py                # 演示脚本
├── test_gradio.py         # 测试文件
├── requirements.txt       # 依赖列表
├── README.md              # 使用说明
└── CHANGELOG.md           # 更新日志
```

### 🐛 已知问题

- Tabs组件不支持作为输入组件，已移除相关功能
- 流式输出在某些情况下可能有延迟
- 需要确保后端API服务正常运行

### 📝 使用建议

1. 建议使用V2版本(`gradio_app_v2.py`)
2. 确保安装所有依赖：`pip install -r requirements.txt`
3. 根据需要修改`config.py`中的配置
4. 首次使用建议运行`demo.py`了解功能

### 🔮 未来计划

- [ ] 添加更多模型支持
- [ ] 实现更丰富的流式效果
- [ ] 添加用户认证功能
- [ ] 支持文件上传
- [ ] 添加对话导出功能
